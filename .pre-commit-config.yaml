# To use:
#
#     pre-commit run -a
#
# Or:
#
#     pre-commit install  # (runs every time you commit in git)
#
# To update this file:
#
#     pre-commit autoupdate
#
# See https://github.com/pre-commit/pre-commit

repos:
  # Standard hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks.git
    rev: v5.0.0
    hooks:
      - id: check-added-large-files
      - id: check-case-conflict
      - id: check-merge-conflict
      - id: check-symlinks
      - id: check-yaml
      - id: debug-statements
      - id: end-of-file-fixer
      - id: mixed-line-ending
      - id: trailing-whitespace

  - repo: https://github.com/psf/black
    rev: 25.1.0
    hooks:
      - id: black

  - repo: https://github.com/Lucas-C/pre-commit-hooks-markup
    rev: v1.0.1
    hooks:
      - id: rst-linter
        exclude: .*/doc/.*

  - repo: local
    hooks:
      - id: clang-format
        name: clang-format
        description: Format files with ClangFormat.
        entry: clang-format
        language: system
        files: \.(c|cc|cxx|cpp|frag|glsl|h|hpp|hxx|ih|ispc|ipp|java|js|m|proto|vert)$
        args: [ "-fallback-style=none", "-i" ]
