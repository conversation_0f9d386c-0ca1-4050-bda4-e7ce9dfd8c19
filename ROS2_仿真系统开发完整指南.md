# ROS2 仿真系统开发完整指南

基于 Tide Controls 项目的 ROS2 机器人仿真系统技术文档

## 目录

1. [项目仿真功能分析](#1-项目仿真功能分析)
2. [URDF/Xacro机器人模型分析](#2-urdfxacro机器人模型分析)
3. [Gazebo世界文件和插件分析](#3-gazebo世界文件和插件分析)
4. [Launch文件仿真启动流程分析](#4-launch文件仿真启动流程分析)
5. [控制器配置和参数分析](#5-控制器配置和参数分析)
6. [从零实现仿真系统完整流程](#6-从零实现仿真系统完整流程)
7. [测试和验证方法](#7-测试和验证方法)

---

## 1. 项目仿真功能分析

### 1.1 仿真相关包结构

项目包含以下仿真相关的核心包：

#### **tide_gazebo** - Gazebo仿真环境包
- **功能**: 提供Gazebo仿真环境、世界文件和启动脚本
- **主要组件**:
  - `launch/gazebo_start.launch.py`: Gazebo启动脚本
  - `world/`: 包含三种比赛环境世界文件
    - RMUC2024_world (RoboMaster大学生机甲大师赛2024)
    - RMUL2024_world (RoboMaster大学生联盟赛2024)
    - RMUL2025_world (RoboMaster大学生联盟赛2025)
  - `meshes/`: 世界环境和障碍物的3D模型文件
- **依赖**: gazebo_ros_pkgs, tide_robot_description

#### **tide_robot_description** - 机器人描述包
- **功能**: 定义机器人的URDF/Xacro模型文件
- **主要组件**:
  - `urdf/base/`: 基础机器人模型文件
  - `urdf/sim/`: 仿真专用模型文件（包含Gazebo插件）
  - `meshes/`: 机器人各部件的3D模型文件
- **支持的机器人类型**: hero（英雄机器人）、infantry（步兵机器人）、sentry（哨兵机器人）

#### **tide_ctrl_bringup** - 控制系统启动包
- **功能**: 统一的机器人控制系统启动入口
- **主要组件**:
  - `launch/tide_ctrl_bringup.launch.py`: 主启动文件
  - `config/`: 不同机器人类型的控制器配置文件
  - `description/`: 机器人描述文件（包含ros2_control配置）

### 1.2 仿真系统架构

```
仿真系统架构:
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Gazebo仿真    │    │   机器人模型    │    │   控制系统      │
│                 │    │                 │    │                 │
│ • 物理引擎      │◄──►│ • URDF/Xacro    │◄──►│ • ros2_control  │
│ • 世界环境      │    │ • 传感器模型    │    │ • 控制器       │
│ • 插件系统      │    │ • 执行器模型    │    │ • 参数配置      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

---

## 2. URDF/Xacro机器人模型分析

### 2.1 模型文件结构

项目采用分层的URDF/Xacro文件结构：

```
tide_robot_description/urdf/
├── base/                           # 基础模型文件
│   ├── hero_description_base.urdf.xacro      # 英雄机器人基础模型
│   ├── infantry_description_base.urdf.xacro  # 步兵机器人基础模型
│   └── sentry_description_base.urdf.xacro    # 哨兵机器人基础模型
└── sim/                            # 仿真专用模型文件
    ├── hero_description_sim.urdf.xacro       # 英雄机器人仿真模型
    ├── infantry_description_sim.urdf.xacro   # 步兵机器人仿真模型
    └── sentry_description_sim.urdf.xacro     # 哨兵机器人仿真模型
```

### 2.2 机器人类型分析

#### **Hero（英雄机器人）**
- **特点**: 大型机器人，具有强大的火力和防护能力
- **主要关节**:
  - `yaw_joint`: 云台偏航关节（连续旋转）
  - `pitch_joint`: 云台俯仰关节（连续旋转）
  - `wheel1-4_joint`: 四个麦克纳姆轮关节
  - `friction_wheel_*_joint`: 摩擦轮关节（发射机构）
  - `loader_joint`: 供弹机构关节

#### **Infantry（步兵机器人）**
- **特点**: 中型机器人，机动性强，适合快速移动和射击
- **主要关节**:
  - `yaw_joint`: 云台偏航关节
  - `pitch_joint`: 云台俯仰关节
  - `friction_wheel1-2_joint`: 两个摩擦轮关节

#### **Sentry（哨兵机器人）**
- **特点**: 双云台设计，具有360度全方位攻击能力
- **主要关节**:
  - `bigyaw_joint`: 大偏航关节（整体旋转）
  - `pitch1_joint`, `yaw1_joint`: 右侧云台关节
  - `pitch2_joint`, `yaw2_joint`: 左侧云台关节
  - `wheel1-4_joint`: 四个麦克纳姆轮关节
  - `friction_wheel1-4_joint`: 四个摩擦轮关节
  - `loader1-2_joint`: 两个供弹机构关节

### 2.3 仿真模型特殊配置

仿真模型文件在基础模型基础上添加了Gazebo专用配置：

```xml
<!-- 示例：哨兵机器人仿真模型配置 -->
<gazebo>
  <!-- ros2_control插件 -->
  <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
    <parameters>$(find tide_ctrl_bringup)/config/sentry/sentry_sim.yaml</parameters>
  </plugin>
</gazebo>

<!-- IMU传感器 -->
<gazebo reference="imu_link">
  <sensor name="mid360_imu" type="imu">
    <always_on>true</always_on>
    <update_rate>100</update_rate>
    <plugin name="imu_plugin" filename="libgazebo_ros_imu_sensor.so">
      <ros>
        <remapping>~/out:=/livox/imu</remapping>
      </ros>
    </plugin>
  </sensor>
</gazebo>

<!-- 底盘运动控制插件 -->
<gazebo>
  <plugin name="mecanum_controller" filename="libgazebo_ros_planar_move.so">
    <ros>
      <remapping>cmd_vel:=cmd_vel_chassis</remapping>
      <remapping>odom:=odom</remapping>
    </ros>
    <update_rate>1000</update_rate>
    <publish_rate>1000</publish_rate>
  </plugin>
</gazebo>
```

---

## 3. Gazebo世界文件和插件分析

### 3.1 世界文件结构

项目包含三种比赛环境的世界文件：

#### **RMUC2024_world** - 机甲大师赛2024环境
- **文件**: `tide_gazebo/world/RMUC2024_world/RMUC2024_world.world`
- **特点**: 
  - 复杂的3D地形环境
  - 多种障碍物和掩体
  - 机器人初始位置: (6.35, 7.6, 0.2)

#### **RMUL2024_world** - 大学生联盟赛2024环境
- **文件**: `tide_gazebo/world/RMUL2024_world/RMUL2024_world.world`
- **特点**:
  - 标准比赛场地
  - 支持动态障碍物版本
  - 机器人初始位置: (4.3, 3.35, 1.1)

#### **RMUL2025_world** - 大学生联盟赛2025环境
- **文件**: `tide_gazebo/world/RMUL2025_world/RMUL2025_world.world`
- **特点**:
  - 最新的比赛环境
  - 简化的地面平面设计
  - 机器人初始位置: (2.0, 7.5, 0.7)

### 3.2 物理参数配置

所有世界文件都包含标准的物理参数设置：

```xml
<physics type='ode'>
  <max_step_size>0.001</max_step_size>        <!-- 最大时间步长 -->
  <real_time_factor>1</real_time_factor>      <!-- 实时因子 -->
  <real_time_update_rate>1000</real_time_update_rate>  <!-- 更新频率 -->
</physics>

<scene>
  <ambient>0.4 0.4 0.4 1</ambient>           <!-- 环境光照 -->
  <background>0.7 0.7 0.7 1</background>     <!-- 背景颜色 -->
  <shadows>1</shadows>                        <!-- 阴影开启 -->
</scene>

<gravity>0 0 -9.8</gravity>                  <!-- 重力加速度 -->
```

---

## 4. Launch文件仿真启动流程分析

### 4.1 主启动文件分析

`tide_ctrl_bringup.launch.py` 是系统的主入口，支持仿真和实物两种模式：

```python
# 关键参数定义
robot_type = LaunchConfiguration("robot_type")      # 机器人类型: sentry/hero/infantry
sim_mode = LaunchConfiguration("sim_mode")          # 仿真模式: true/false
use_sim_time = LaunchConfiguration("use_sim_time")  # 使用仿真时间

# 动态选择描述文件
sim_or_real = PythonExpression(["'_sim' if '", sim_mode, "' == 'true' else '_real'"])
xacro_file = PathJoinSubstitution([
    robot_description_path,
    PythonExpression(["str('", robot_type, "') + '", sim_or_real, "' + '.xacro'"])
])
```

### 4.2 Gazebo启动流程

`gazebo_start.launch.py` 负责启动Gazebo仿真环境：

```python
# 环境变量配置
append_environment_plugin = AppendEnvironmentVariable(
    "GAZEBO_PLUGIN_PATH", 
    os.path.join(get_package_share_directory("tide_gazebo"), "meshes", "obstacles", "obstacle_plugin", "lib")
)

append_environment_modules = AppendEnvironmentVariable(
    "GAZEBO_MODEL_PATH",
    os.path.join(get_package_share_directory("tide_robot_description"), "meshes")
)

# 世界选择和机器人生成
def create_gazebo_launch_group(world_type):
    world_config = get_world_config(world_type)
    return GroupAction([
        Node(package="gazebo_ros", executable="spawn_entity.py",
             arguments=["-entity", "robot", "-topic", "robot_description",
                       "-x", world_config["x"], "-y", world_config["y"], "-z", world_config["z"]]),
        IncludeLaunchDescription(PythonLaunchDescriptionSource(
            os.path.join(pkg_gazebo_ros, "launch", "gzserver.launch.py")),
            launch_arguments={"world": os.path.join(bringup_dir, "world", world_config["world_path"])}.items())
    ])
```

### 4.3 启动顺序

1. **参数声明和配置**
2. **环境变量设置** (GAZEBO_PLUGIN_PATH, GAZEBO_MODEL_PATH)
3. **机器人描述文件处理** (xacro → URDF)
4. **robot_state_publisher启动**
5. **控制器管理器启动** (仅实物模式)
6. **Gazebo服务器和客户端启动** (仅仿真模式)
7. **机器人实体生成**
8. **控制器spawner启动**
9. **Foxglove桥接启动** (可视化)

---

## 5. 控制器配置和参数分析

### 5.1 控制器类型

项目使用自定义的控制器类型：

- **tide_gimbal_controller/TideGimbalController**: 云台控制器
- **tide_shooter_controller/TideShooterController**: 射击控制器
- **joint_state_broadcaster/JointStateBroadcaster**: 关节状态广播器

### 5.2 仿真vs实物配置差异

#### **仿真配置特点**:
```yaml
# 示例：hero_sim.yaml
controller_manager:
  ros__parameters:
    update_rate: 1000  # 高频率更新

gimbal_controller:
  ros__parameters:
    open_loop: true    # 开环控制（仿真中更稳定）
    
    pitch:
      joint: "pitch_joint"
      max: 0.28
      min: -0.28
      scan_range: [-0.24, 0.24]  # 扫描范围
      scan_add: 0.001            # 扫描步长
```

#### **实物配置特点**:
```yaml
# 示例：hero_real.yaml
gimbal_controller:
  ros__parameters:
    use_external_state_interface: true  # 使用外部状态接口
    
    pitch:
      joint: "pitch_joint"
      reverse: true              # 反向控制
      pid:                       # PID参数
        p: 60000.0
        i: 0.0
        d: 0.0
```

### 5.3 ros2_control配置

#### **仿真模式**:
```xml
<ros2_control name="GazeboSystem" type="system">
  <hardware>
    <plugin>gazebo_ros2_control/GazeboSystem</plugin>
  </hardware>
  
  <joint name="pitch_joint">
    <command_interface name="position"/>
    <state_interface name="position"/>
    <state_interface name="velocity"/>
    <state_interface name="effort"/>
  </joint>
</ros2_control>
```

#### **实物模式**:
```xml
<ros2_control name="TideHardwareInterface" type="system">
  <hardware>
    <plugin>tide_hw_interface/TideHardwareInterface</plugin>
    <param name="can_device_count">0</param>
  </hardware>
  
  <joint name="pitch_joint">
    <param name="can_bus">can0</param>
    <param name="tx_id">3</param>
    <param name="motor_type">GM6020</param>
    <command_interface name="position"/>
    <state_interface name="position"/>
  </joint>
</ros2_control>
```

---

## 6. 从零实现仿真系统完整流程

### 6.1 环境准备

#### **系统要求**:
- Ubuntu 22.04 LTS
- ROS2 Humble
- Gazebo 11
- Python 3.10+

#### **依赖安装**:
```bash
# ROS2基础包
sudo apt install ros-humble-desktop-full

# Gazebo相关包
sudo apt install ros-humble-gazebo-ros-pkgs
sudo apt install ros-humble-gazebo-ros2-control

# 控制相关包
sudo apt install ros-humble-controller-manager
sudo apt install ros-humble-joint-state-broadcaster
sudo apt install ros-humble-position-controllers

# 其他工具
sudo apt install ros-humble-xacro
sudo apt install ros-humble-robot-state-publisher
```

### 6.2 步骤1: 创建包结构

```bash
# 创建工作空间
mkdir -p ~/robot_sim_ws/src
cd ~/robot_sim_ws/src

# 创建仿真相关包
ros2 pkg create --build-type ament_cmake robot_gazebo
ros2 pkg create --build-type ament_cmake robot_description
ros2 pkg create --build-type ament_cmake robot_bringup

# 创建目录结构
cd robot_gazebo
mkdir -p launch world meshes
cd ../robot_description
mkdir -p urdf/base urdf/sim meshes launch
cd ../robot_bringup
mkdir -p launch config description
```

### 6.3 步骤2: 编写基础URDF模型

创建 `robot_description/urdf/base/robot_base.urdf.xacro`:

```xml
<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="robot">
  
  <!-- 基础链接 -->
  <link name="base_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="5.0"/>
      <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.5 0.3 0.2"/>
      </geometry>
      <material name="blue">
        <color rgba="0 0 1 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.5 0.3 0.2"/>
      </geometry>
    </collision>
  </link>

  <!-- 云台偏航关节 -->
  <joint name="yaw_joint" type="continuous">
    <origin xyz="0 0 0.15" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child link="yaw_link"/>
    <axis xyz="0 0 1"/>
  </joint>

  <link name="yaw_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="1.0"/>
      <inertia ixx="0.01" ixy="0" ixz="0" iyy="0.01" iyz="0" izz="0.01"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="red">
        <color rgba="1 0 0 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
    </collision>
  </link>

  <!-- 云台俯仰关节 -->
  <joint name="pitch_joint" type="continuous">
    <origin xyz="0.1 0 0" rpy="0 0 0"/>
    <parent link="yaw_link"/>
    <child link="pitch_link"/>
    <axis xyz="0 1 0"/>
  </joint>

  <link name="pitch_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.5"/>
      <inertia ixx="0.005" ixy="0" ixz="0" iyy="0.005" iyz="0" izz="0.005"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.2 0.1 0.1"/>
      </geometry>
      <material name="green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.2 0.1 0.1"/>
      </geometry>
    </collision>
  </link>

</robot>
```

### 6.4 步骤3: 创建仿真专用模型

创建 `robot_description/urdf/sim/robot_sim.urdf.xacro`:

```xml
<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="robot">
  
  <!-- 包含基础模型 -->
  <xacro:include filename="$(find robot_description)/urdf/base/robot_base.urdf.xacro"/>
  
  <!-- Gazebo ros2_control插件 -->
  <gazebo>
    <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
      <parameters>$(find robot_bringup)/config/robot_sim.yaml</parameters>
    </plugin>
  </gazebo>
  
  <!-- 材质定义 -->
  <gazebo reference="base_link">
    <material>Gazebo/Blue</material>
  </gazebo>
  
  <gazebo reference="yaw_link">
    <material>Gazebo/Red</material>
  </gazebo>
  
  <gazebo reference="pitch_link">
    <material>Gazebo/Green</material>
  </gazebo>

</robot>
```

### 6.5 步骤4: 配置ros2_control

创建 `robot_bringup/description/robot_sim.xacro`:

```xml
<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="robot">
  
  <!-- 包含仿真模型 -->
  <xacro:include filename="$(find robot_description)/urdf/sim/robot_sim.urdf.xacro"/>
  
  <!-- ros2_control配置 -->
  <ros2_control name="GazeboSystem" type="system">
    <hardware>
      <plugin>gazebo_ros2_control/GazeboSystem</plugin>
    </hardware>
    
    <joint name="yaw_joint">
      <command_interface name="position"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    
    <joint name="pitch_joint">
      <command_interface name="position"/>
      <state_interface name="position"/>
      <state_interface name="velocity"/>
      <state_interface name="effort"/>
    </joint>
    
  </ros2_control>

</robot>
```

### 6.6 步骤5: 创建控制器配置

创建 `robot_bringup/config/robot_sim.yaml`:

```yaml
controller_manager:
  ros__parameters:
    update_rate: 1000  # Hz
    
    joint_state_broadcaster:
      type: "joint_state_broadcaster/JointStateBroadcaster"
    
    position_controller:
      type: "position_controllers/JointGroupPositionController"

position_controller:
  ros__parameters:
    joints:
      - yaw_joint
      - pitch_joint
```

### 6.7 步骤6: 创建世界文件

创建 `robot_gazebo/world/empty_world.world`:

```xml
<?xml version="1.0"?>
<sdf version='1.7'>
  <world name='default'>
    
    <!-- 物理引擎配置 -->
    <physics type='ode'>
      <max_step_size>0.001</max_step_size>
      <real_time_factor>1</real_time_factor>
      <real_time_update_rate>1000</real_time_update_rate>
    </physics>
    
    <!-- 场景配置 -->
    <scene>
      <ambient>0.4 0.4 0.4 1</ambient>
      <background>0.7 0.7 0.7 1</background>
      <shadows>1</shadows>
    </scene>
    
    <!-- 重力 -->
    <gravity>0 0 -9.81</gravity>
    
    <!-- 地面 -->
    <model name='ground_plane'>
      <static>1</static>
      <link name='link'>
        <collision name='collision'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
        </collision>
        <visual name='visual'>
          <geometry>
            <plane>
              <normal>0 0 1</normal>
              <size>100 100</size>
            </plane>
          </geometry>
          <material>
            <script>
              <uri>file://media/materials/scripts/gazebo.material</uri>
              <name>Gazebo/Grey</name>
            </script>
          </material>
        </visual>
      </link>
    </model>
    
    <!-- 光照 -->
    <light name='sun' type='directional'>
      <cast_shadows>1</cast_shadows>
      <pose>0 0 10 0 -0 0</pose>
      <diffuse>0.8 0.8 0.8 1</diffuse>
      <specular>0.2 0.2 0.2 1</specular>
      <attenuation>
        <range>1000</range>
        <constant>0.9</constant>
        <linear>0.01</linear>
        <quadratic>0.001</quadratic>
      </attenuation>
      <direction>-0.5 0.1 -0.9</direction>
    </light>
    
  </world>
</sdf>
```

### 6.8 步骤7: 创建Gazebo启动文件

创建 `robot_gazebo/launch/gazebo.launch.py`:

```python
import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription, DeclareLaunchArgument
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration
from launch_ros.actions import Node

def generate_launch_description():
    # 获取包路径
    gazebo_pkg_dir = get_package_share_directory('robot_gazebo')
    gazebo_ros_dir = get_package_share_directory('gazebo_ros')
    
    # 参数
    use_sim_time = LaunchConfiguration('use_sim_time')
    world_file = LaunchConfiguration('world')
    
    # 参数声明
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value='true',
        description='Use simulation (Gazebo) clock if true'
    )
    
    declare_world = DeclareLaunchArgument(
        'world',
        default_value=os.path.join(gazebo_pkg_dir, 'world', 'empty_world.world'),
        description='Full path to world model file to load'
    )
    
    # Gazebo服务器
    gzserver = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(gazebo_ros_dir, 'launch', 'gzserver.launch.py')
        ),
        launch_arguments={'world': world_file}.items()
    )
    
    # Gazebo客户端
    gzclient = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(gazebo_ros_dir, 'launch', 'gzclient.launch.py')
        )
    )
    
    # 机器人生成
    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=['-topic', 'robot_description',
                  '-entity', 'robot',
                  '-x', '0', '-y', '0', '-z', '0.5'],
        output='screen'
    )
    
    return LaunchDescription([
        declare_use_sim_time,
        declare_world,
        gzserver,
        gzclient,
        spawn_entity
    ])
```

### 6.9 步骤8: 创建主启动文件

创建 `robot_bringup/launch/robot_bringup.launch.py`:

```python
import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import DeclareLaunchArgument, IncludeLaunchDescription
from launch.conditions import IfCondition
from launch.launch_description_sources import PythonLaunchDescriptionSource
from launch.substitutions import LaunchConfiguration, Command, FindExecutable, PathJoinSubstitution
from launch_ros.actions import Node

def generate_launch_description():
    # 获取包路径
    bringup_dir = get_package_share_directory('robot_bringup')
    gazebo_dir = get_package_share_directory('robot_gazebo')
    
    # 参数
    sim_mode = LaunchConfiguration('sim_mode')
    use_sim_time = LaunchConfiguration('use_sim_time')
    
    # 参数声明
    declare_sim_mode = DeclareLaunchArgument(
        'sim_mode',
        default_value='true',
        description='Use simulation mode if true'
    )
    
    declare_use_sim_time = DeclareLaunchArgument(
        'use_sim_time',
        default_value=LaunchConfiguration('sim_mode'),
        description='Use simulation clock if true'
    )
    
    # 机器人描述
    xacro_file = os.path.join(bringup_dir, 'description', 'robot_sim.xacro')
    robot_description = Command([FindExecutable(name='xacro'), ' ', xacro_file])
    
    # robot_state_publisher
    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        name='robot_state_publisher',
        output='screen',
        parameters=[{
            'use_sim_time': use_sim_time,
            'robot_description': robot_description
        }]
    )
    
    # Gazebo启动
    gazebo_launch = IncludeLaunchDescription(
        PythonLaunchDescriptionSource(
            os.path.join(gazebo_dir, 'launch', 'gazebo.launch.py')
        ),
        launch_arguments={
            'use_sim_time': use_sim_time
        }.items(),
        condition=IfCondition(sim_mode)
    )
    
    # 控制器spawner
    joint_state_broadcaster_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['joint_state_broadcaster', '--controller-manager', '/controller_manager'],
    )
    
    position_controller_spawner = Node(
        package='controller_manager',
        executable='spawner',
        arguments=['position_controller', '--controller-manager', '/controller_manager'],
    )
    
    return LaunchDescription([
        declare_sim_mode,
        declare_use_sim_time,
        robot_state_publisher,
        gazebo_launch,
        joint_state_broadcaster_spawner,
        position_controller_spawner
    ])
```

### 6.10 步骤9: 配置package.xml和CMakeLists.txt

#### **robot_gazebo/package.xml**:
```xml
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_gazebo</name>
  <version>1.0.0</version>
  <description>Gazebo simulation package for robot</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>
  
  <depend>gazebo_ros_pkgs</depend>
  <depend>robot_description</depend>
  
  <exec_depend>xacro</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
```

#### **robot_gazebo/CMakeLists.txt**:
```cmake
cmake_minimum_required(VERSION 3.8)
project(robot_gazebo)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

find_package(ament_cmake REQUIRED)

# 安装launch文件、世界文件和模型
install(DIRECTORY
  launch
  world
  meshes
  DESTINATION share/${PROJECT_NAME}/
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
```

### 6.11 步骤10: 编译和测试

```bash
# 编译
cd ~/robot_sim_ws
colcon build

# 设置环境
source install/setup.bash

# 启动仿真
ros2 launch robot_bringup robot_bringup.launch.py sim_mode:=true

# 在新终端中测试控制
ros2 topic pub /position_controller/commands std_msgs/msg/Float64MultiArray "data: [0.5, 0.3]"
```

---

## 7. 测试和验证方法

### 7.1 基础功能测试

#### **1. 仿真环境启动测试**
```bash
# 启动仿真
ros2 launch robot_bringup robot_bringup.launch.py sim_mode:=true

# 检查节点状态
ros2 node list

# 检查话题
ros2 topic list

# 检查服务
ros2 service list
```

#### **2. 机器人模型验证**
```bash
# 检查机器人描述
ros2 topic echo /robot_description

# 检查关节状态
ros2 topic echo /joint_states

# 检查TF树
ros2 run tf2_tools view_frames
```

#### **3. 控制器测试**
```bash
# 检查控制器状态
ros2 control list_controllers

# 测试关节控制
ros2 topic pub /position_controller/commands std_msgs/msg/Float64MultiArray "data: [1.0, 0.5]"

# 监控关节状态
ros2 topic echo /joint_states
```

### 7.2 性能测试

#### **1. 实时性能测试**
```bash
# 检查仿真频率
ros2 topic hz /clock

# 检查控制频率
ros2 topic hz /joint_states

# 检查延迟
ros2 topic delay /joint_states
```

#### **2. 资源使用测试**
```bash
# CPU使用率
top -p $(pgrep -f gazebo)

# 内存使用
ps aux | grep gazebo

# GPU使用（如果有）
nvidia-smi
```

### 7.3 集成测试

#### **1. 多机器人测试**
```bash
# 启动多个机器人实例
ros2 launch robot_bringup robot_bringup.launch.py sim_mode:=true namespace:=robot1
ros2 launch robot_bringup robot_bringup.launch.py sim_mode:=true namespace:=robot2
```

#### **2. 传感器数据测试**
```bash
# 检查IMU数据
ros2 topic echo /imu/data

# 检查里程计数据
ros2 topic echo /odom

# 检查相机数据
ros2 topic echo /camera/image_raw
```

### 7.4 故障排除

#### **常见问题及解决方案**:

1. **Gazebo启动失败**
   - 检查世界文件路径
   - 验证模型文件完整性
   - 检查环境变量设置

2. **机器人模型不显示**
   - 验证URDF语法
   - 检查mesh文件路径
   - 确认材质定义

3. **控制器无响应**
   - 检查ros2_control配置
   - 验证控制器参数
   - 确认硬件接口配置

4. **性能问题**
   - 调整物理引擎参数
   - 优化模型复杂度
   - 检查系统资源

### 7.5 调试工具

#### **可视化工具**:
```bash
# RViz2可视化
ros2 run rviz2 rviz2

# rqt工具集
ros2 run rqt_gui rqt_gui

# 关节状态发布器
ros2 run joint_state_publisher_gui joint_state_publisher_gui
```

#### **日志分析**:
```bash
# 查看节点日志
ros2 log info

# Gazebo日志
tail -f ~/.gazebo/server-*.log

# 系统日志
journalctl -f
```

---

## 总结

本指南基于Tide Controls项目的实际代码结构，提供了完整的ROS2仿真系统开发流程。通过遵循这个指南，开发者可以：

1. **理解现有系统架构** - 深入了解项目的仿真组件和设计思路
2. **快速搭建仿真环境** - 按照步骤创建自己的仿真系统
3. **定制化开发** - 根据具体需求修改和扩展功能
4. **有效调试和测试** - 使用提供的测试方法验证系统功能

该指南适用于机器人仿真开发、RoboMaster比赛准备以及相关的教学和研究项目。

---

## 附录A: 完整配置文件模板

### A.1 package.xml模板

#### **仿真包 (robot_gazebo)**
```xml
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_gazebo</name>
  <version>1.0.0</version>
  <description>Gazebo simulation package for robot</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>gazebo_ros_pkgs</depend>
  <depend>robot_description</depend>

  <exec_depend>xacro</exec_depend>
  <exec_depend>joint_state_publisher</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>

  <export>
    <build_type>ament_cmake</build_type>
    <gazebo_ros
      gazebo_model_path="${prefix}/meshes"
      gazebo_plugin_path="${prefix}/plugins"
    />
  </export>
</package>
```

#### **机器人描述包 (robot_description)**
```xml
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_description</name>
  <version>1.0.0</version>
  <description>Robot description package</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>launch_ros</exec_depend>
  <exec_depend>xacro</exec_depend>
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>gazebo_ros</exec_depend>
  <exec_depend>position_controllers</exec_depend>
  <exec_depend>gazebo_ros2_control</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
```

#### **启动包 (robot_bringup)**
```xml
<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>robot_bringup</name>
  <version>1.0.0</version>
  <description>Robot bringup package</description>
  <maintainer email="<EMAIL>">Your Name</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <exec_depend>robot_description</exec_depend>
  <exec_depend>robot_gazebo</exec_depend>
  <exec_depend>controller_manager</exec_depend>
  <exec_depend>joint_state_broadcaster</exec_depend>
  <exec_depend>position_controllers</exec_depend>
  <exec_depend>robot_state_publisher</exec_depend>
  <exec_depend>xacro</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
```

### A.2 CMakeLists.txt模板

#### **通用CMakeLists.txt**
```cmake
cmake_minimum_required(VERSION 3.8)
project(robot_gazebo)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# 查找依赖
find_package(ament_cmake REQUIRED)

# 安装目录
install(DIRECTORY
  launch
  world
  meshes
  config
  DESTINATION share/${PROJECT_NAME}/
)

# 测试
if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
```

### A.3 高级URDF模板

#### **带传感器的机器人模型**
```xml
<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="advanced_robot">

  <!-- 参数定义 -->
  <xacro:property name="base_width" value="0.5"/>
  <xacro:property name="base_length" value="0.3"/>
  <xacro:property name="base_height" value="0.2"/>
  <xacro:property name="wheel_radius" value="0.05"/>
  <xacro:property name="wheel_width" value="0.03"/>

  <!-- 宏定义：轮子 -->
  <xacro:macro name="wheel" params="prefix x y z">
    <link name="${prefix}_wheel_link">
      <inertial>
        <origin xyz="0 0 0" rpy="0 0 0"/>
        <mass value="0.5"/>
        <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
      </inertial>
      <visual>
        <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
        <material name="black">
          <color rgba="0 0 0 1"/>
        </material>
      </visual>
      <collision>
        <origin xyz="0 0 0" rpy="${pi/2} 0 0"/>
        <geometry>
          <cylinder radius="${wheel_radius}" length="${wheel_width}"/>
        </geometry>
      </collision>
    </link>

    <joint name="${prefix}_wheel_joint" type="continuous">
      <origin xyz="${x} ${y} ${z}" rpy="0 0 0"/>
      <parent link="base_link"/>
      <child link="${prefix}_wheel_link"/>
      <axis xyz="0 1 0"/>
    </joint>

    <!-- Gazebo配置 -->
    <gazebo reference="${prefix}_wheel_link">
      <material>Gazebo/Black</material>
      <mu1>1.0</mu1>
      <mu2>1.0</mu2>
    </gazebo>
  </xacro:macro>

  <!-- 基础链接 -->
  <link name="base_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="10.0"/>
      <inertia ixx="0.1" ixy="0" ixz="0" iyy="0.1" iyz="0" izz="0.1"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
      <material name="blue">
        <color rgba="0 0 1 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="${base_length} ${base_width} ${base_height}"/>
      </geometry>
    </collision>
  </link>

  <!-- 使用轮子宏 -->
  <xacro:wheel prefix="front_left" x="${base_length/2}" y="${base_width/2}" z="-${base_height/2}"/>
  <xacro:wheel prefix="front_right" x="${base_length/2}" y="-${base_width/2}" z="-${base_height/2}"/>
  <xacro:wheel prefix="rear_left" x="-${base_length/2}" y="${base_width/2}" z="-${base_height/2}"/>
  <xacro:wheel prefix="rear_right" x="-${base_length/2}" y="-${base_width/2}" z="-${base_height/2}"/>

  <!-- 激光雷达 -->
  <link name="lidar_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.1"/>
      <inertia ixx="0.001" ixy="0" ixz="0" iyy="0.001" iyz="0" izz="0.001"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
      <material name="red">
        <color rgba="1 0 0 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <cylinder radius="0.05" length="0.1"/>
      </geometry>
    </collision>
  </link>

  <joint name="lidar_joint" type="fixed">
    <origin xyz="0 0 ${base_height/2 + 0.05}" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child link="lidar_link"/>
  </joint>

  <!-- 相机 -->
  <link name="camera_link">
    <inertial>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <mass value="0.05"/>
      <inertia ixx="0.0001" ixy="0" ixz="0" iyy="0.0001" iyz="0" izz="0.0001"/>
    </inertial>
    <visual>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.1 0.05"/>
      </geometry>
      <material name="green">
        <color rgba="0 1 0 1"/>
      </material>
    </visual>
    <collision>
      <origin xyz="0 0 0" rpy="0 0 0"/>
      <geometry>
        <box size="0.05 0.1 0.05"/>
      </geometry>
    </collision>
  </link>

  <joint name="camera_joint" type="fixed">
    <origin xyz="${base_length/2} 0 0" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child link="camera_link"/>
  </joint>

  <!-- IMU -->
  <link name="imu_link"/>
  <joint name="imu_joint" type="fixed">
    <origin xyz="0 0 0" rpy="0 0 0"/>
    <parent link="base_link"/>
    <child link="imu_link"/>
  </joint>

</robot>
```

### A.4 高级Gazebo插件配置

#### **带传感器的仿真模型**
```xml
<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="advanced_robot_sim">

  <!-- 包含基础模型 -->
  <xacro:include filename="$(find robot_description)/urdf/base/advanced_robot_base.urdf.xacro"/>

  <!-- 差分驱动插件 -->
  <gazebo>
    <plugin name="differential_drive_controller" filename="libgazebo_ros_diff_drive.so">
      <ros>
        <namespace>/</namespace>
        <remapping>cmd_vel:=cmd_vel</remapping>
        <remapping>odom:=odom</remapping>
      </ros>

      <!-- 轮子参数 -->
      <left_joint>front_left_wheel_joint</left_joint>
      <left_joint>rear_left_wheel_joint</left_joint>
      <right_joint>front_right_wheel_joint</right_joint>
      <right_joint>rear_right_wheel_joint</right_joint>

      <!-- 物理参数 -->
      <wheel_separation>0.5</wheel_separation>
      <wheel_diameter>0.1</wheel_diameter>

      <!-- 限制 -->
      <max_wheel_torque>20</max_wheel_torque>
      <max_wheel_acceleration>1.0</max_wheel_acceleration>

      <!-- 输出 -->
      <publish_odom>true</publish_odom>
      <publish_odom_tf>true</publish_odom_tf>
      <publish_wheel_tf>true</publish_wheel_tf>

      <odometry_frame>odom</odometry_frame>
      <robot_base_frame>base_link</robot_base_frame>
    </plugin>
  </gazebo>

  <!-- 激光雷达插件 -->
  <gazebo reference="lidar_link">
    <sensor name="lidar" type="ray">
      <always_on>true</always_on>
      <update_rate>10</update_rate>
      <ray>
        <scan>
          <horizontal>
            <samples>360</samples>
            <resolution>1</resolution>
            <min_angle>-3.14159</min_angle>
            <max_angle>3.14159</max_angle>
          </horizontal>
        </scan>
        <range>
          <min>0.1</min>
          <max>10.0</max>
          <resolution>0.01</resolution>
        </range>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.01</stddev>
        </noise>
      </ray>
      <plugin name="lidar_controller" filename="libgazebo_ros_ray_sensor.so">
        <ros>
          <remapping>~/out:=scan</remapping>
        </ros>
        <output_type>sensor_msgs/LaserScan</output_type>
        <frame_name>lidar_link</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- 相机插件 -->
  <gazebo reference="camera_link">
    <sensor name="camera" type="camera">
      <always_on>true</always_on>
      <update_rate>30</update_rate>
      <camera>
        <horizontal_fov>1.3962634</horizontal_fov>
        <image>
          <width>640</width>
          <height>480</height>
          <format>R8G8B8</format>
        </image>
        <clip>
          <near>0.02</near>
          <far>300</far>
        </clip>
        <noise>
          <type>gaussian</type>
          <mean>0.0</mean>
          <stddev>0.007</stddev>
        </noise>
      </camera>
      <plugin name="camera_controller" filename="libgazebo_ros_camera.so">
        <ros>
          <remapping>image_raw:=camera/image_raw</remapping>
          <remapping>camera_info:=camera/camera_info</remapping>
        </ros>
        <camera_name>camera</camera_name>
        <frame_name>camera_link</frame_name>
        <hack_baseline>0.07</hack_baseline>
      </plugin>
    </sensor>
  </gazebo>

  <!-- IMU插件 -->
  <gazebo reference="imu_link">
    <sensor name="imu" type="imu">
      <always_on>true</always_on>
      <update_rate>100</update_rate>
      <plugin name="imu_controller" filename="libgazebo_ros_imu_sensor.so">
        <ros>
          <remapping>~/out:=imu/data</remapping>
        </ros>
        <frame_name>imu_link</frame_name>
      </plugin>
    </sensor>
  </gazebo>

  <!-- ros2_control插件 -->
  <gazebo>
    <plugin filename="libgazebo_ros2_control.so" name="gazebo_ros2_control">
      <parameters>$(find robot_bringup)/config/robot_sim.yaml</parameters>
    </plugin>
  </gazebo>

</robot>
```

---

## 附录B: 常用命令和工具

### B.1 ROS2基础命令

#### **节点管理**
```bash
# 列出所有节点
ros2 node list

# 查看节点信息
ros2 node info /node_name

# 运行节点
ros2 run package_name executable_name
```

#### **话题操作**
```bash
# 列出所有话题
ros2 topic list

# 查看话题信息
ros2 topic info /topic_name

# 发布话题
ros2 topic pub /topic_name msg_type "data"

# 订阅话题
ros2 topic echo /topic_name

# 查看话题频率
ros2 topic hz /topic_name
```

#### **服务操作**
```bash
# 列出所有服务
ros2 service list

# 调用服务
ros2 service call /service_name service_type "request"

# 查看服务类型
ros2 service type /service_name
```

#### **参数操作**
```bash
# 列出参数
ros2 param list

# 获取参数值
ros2 param get /node_name parameter_name

# 设置参数值
ros2 param set /node_name parameter_name value
```

### B.2 控制器管理命令

#### **控制器操作**
```bash
# 列出所有控制器
ros2 control list_controllers

# 列出硬件组件
ros2 control list_hardware_components

# 列出硬件接口
ros2 control list_hardware_interfaces

# 加载控制器
ros2 control load_controller controller_name

# 配置控制器
ros2 control configure_controller controller_name

# 启动控制器
ros2 control switch_controllers --start controller_name

# 停止控制器
ros2 control switch_controllers --stop controller_name
```

#### **Spawner使用**
```bash
# 启动控制器
ros2 run controller_manager spawner controller_name

# 启动多个控制器
ros2 run controller_manager spawner controller1 controller2

# 指定控制器管理器
ros2 run controller_manager spawner controller_name --controller-manager /controller_manager
```

### B.3 Gazebo相关命令

#### **Gazebo操作**
```bash
# 启动Gazebo服务器
ros2 launch gazebo_ros gzserver.launch.py world:=world_file.world

# 启动Gazebo客户端
ros2 launch gazebo_ros gzclient.launch.py

# 生成模型
ros2 run gazebo_ros spawn_entity.py -topic robot_description -entity robot_name

# 删除模型
ros2 service call /delete_entity gazebo_msgs/srv/DeleteEntity "name: 'robot_name'"

# 暂停仿真
ros2 service call /pause_physics std_srvs/srv/Empty

# 恢复仿真
ros2 service call /unpause_physics std_srvs/srv/Empty

# 重置仿真
ros2 service call /reset_simulation std_srvs/srv/Empty
```

### B.4 调试和可视化工具

#### **RViz2**
```bash
# 启动RViz2
ros2 run rviz2 rviz2

# 使用配置文件启动
ros2 run rviz2 rviz2 -d config_file.rviz
```

#### **rqt工具**
```bash
# rqt主界面
ros2 run rqt_gui rqt_gui

# 话题监控
ros2 run rqt_topic rqt_topic

# 图形界面
ros2 run rqt_graph rqt_graph

# 控制台
ros2 run rqt_console rqt_console

# 参数重配置
ros2 run rqt_reconfigure rqt_reconfigure
```

#### **TF工具**
```bash
# 查看TF树
ros2 run tf2_tools view_frames

# TF监控
ros2 run tf2_ros tf2_monitor

# TF回显
ros2 run tf2_ros tf2_echo source_frame target_frame
```

### B.5 构建和测试命令

#### **Colcon构建**
```bash
# 构建所有包
colcon build

# 构建特定包
colcon build --packages-select package_name

# 构建并运行测试
colcon build --packages-select package_name --cmake-args -DBUILD_TESTING=ON
colcon test --packages-select package_name

# 清理构建
rm -rf build install log
```

#### **测试命令**
```bash
# 运行测试
colcon test

# 查看测试结果
colcon test-result --verbose

# 运行特定测试
ros2 run package_name test_executable
```

---

## 附录C: 故障排除指南

### C.1 常见错误及解决方案

#### **1. Gazebo启动问题**

**错误**: `[Err] [REST.cc:205] Error in REST request`
**解决方案**:
```bash
# 检查网络连接
ping google.com

# 设置离线模式
export GAZEBO_MODEL_DATABASE_URI=""
```

**错误**: `libGL error: No matching fbConfigs or visuals found`
**解决方案**:
```bash
# 安装Mesa库
sudo apt install mesa-utils

# 检查OpenGL支持
glxinfo | grep "OpenGL version"
```

#### **2. URDF/Xacro问题**

**错误**: `[ERROR] [robot_state_publisher]: URDF file is not valid`
**解决方案**:
```bash
# 检查URDF语法
check_urdf robot.urdf

# 转换xacro到urdf
ros2 run xacro xacro robot.xacro > robot.urdf

# 验证URDF
urdf_to_graphiz robot.urdf
```

#### **3. 控制器问题**

**错误**: `Controller 'controller_name' is not available`
**解决方案**:
```bash
# 检查控制器配置
ros2 control list_controllers

# 重新加载控制器
ros2 control load_controller controller_name
ros2 control configure_controller controller_name
ros2 control switch_controllers --start controller_name
```

#### **4. TF问题**

**错误**: `Lookup would require extrapolation into the future`
**解决方案**:
```bash
# 检查时间同步
ros2 param get /use_sim_time

# 检查TF发布
ros2 topic echo /tf
ros2 topic echo /tf_static
```

### C.2 性能优化建议

#### **1. Gazebo性能优化**
```xml
<!-- 在世界文件中添加 -->
<physics type='ode'>
  <max_step_size>0.001</max_step_size>
  <real_time_factor>1</real_time_factor>
  <real_time_update_rate>1000</real_time_update_rate>
  <ode>
    <solver>
      <type>quick</type>
      <iters>10</iters>
      <sor>1.3</sor>
    </solver>
  </ode>
</physics>
```

#### **2. 模型优化**
- 减少mesh复杂度
- 使用简化的碰撞几何体
- 合理设置惯性参数
- 避免过小的质量值

#### **3. 控制器优化**
```yaml
controller_manager:
  ros__parameters:
    update_rate: 100  # 降低更新频率以提高性能
```

### C.3 日志和监控

#### **1. 启用详细日志**
```bash
# 设置日志级别
export RCUTILS_LOGGING_SEVERITY=DEBUG

# Gazebo详细日志
export GAZEBO_VERBOSE=1
```

#### **2. 性能监控**
```bash
# CPU使用率
htop

# 内存使用
free -h

# 磁盘I/O
iotop

# 网络监控
nethogs
```

---

## 附录D: 扩展功能实现

### D.1 多机器人仿真

#### **命名空间配置**
```python
# 在launch文件中
def generate_launch_description():
    namespace = LaunchConfiguration('namespace')

    robot_state_publisher = Node(
        package='robot_state_publisher',
        executable='robot_state_publisher',
        namespace=namespace,
        parameters=[{'robot_description': robot_description}]
    )

    spawn_entity = Node(
        package='gazebo_ros',
        executable='spawn_entity.py',
        arguments=[
            '-topic', [namespace, '/robot_description'],
            '-entity', [namespace, '_robot'],
            '-robot_namespace', namespace
        ]
    )
```

### D.2 自定义传感器插件

#### **简单传感器插件示例**
```cpp
#include <gazebo/gazebo.hh>
#include <gazebo/sensors/sensors.hh>
#include <rclcpp/rclcpp.hpp>

namespace gazebo
{
  class CustomSensorPlugin : public SensorPlugin
  {
    public: void Load(sensors::SensorPtr _sensor, sdf::ElementPtr _sdf)
    {
      // 初始化ROS2节点
      if (!rclcpp::ok())
      {
        rclcpp::init(0, nullptr);
      }

      node_ = rclcpp::Node::make_shared("custom_sensor");

      // 创建发布器
      pub_ = node_->create_publisher<sensor_msgs::msg::Range>("range", 10);

      // 连接更新事件
      this->updateConnection_ = _sensor->ConnectUpdated(
        std::bind(&CustomSensorPlugin::OnUpdate, this));
    }

    private: void OnUpdate()
    {
      // 发布传感器数据
      auto msg = sensor_msgs::msg::Range();
      msg.header.stamp = node_->now();
      msg.range = 1.0; // 示例数据
      pub_->publish(msg);
    }

    private: rclcpp::Node::SharedPtr node_;
    private: rclcpp::Publisher<sensor_msgs::msg::Range>::SharedPtr pub_;
    private: event::ConnectionPtr updateConnection_;
  };

  GZ_REGISTER_SENSOR_PLUGIN(CustomSensorPlugin)
}
```

### D.3 动态重配置

#### **参数服务器集成**
```python
from rcl_interfaces.msg import ParameterDescriptor
from rclcpp.parameter import Parameter

class ConfigurableNode(Node):
    def __init__(self):
        super().__init__('configurable_node')

        # 声明参数
        self.declare_parameter('max_velocity', 1.0,
                             ParameterDescriptor(description='Maximum velocity'))

        # 参数回调
        self.add_on_set_parameters_callback(self.parameter_callback)

    def parameter_callback(self, params):
        for param in params:
            if param.name == 'max_velocity':
                self.max_velocity = param.value
        return SetParametersResult(successful=True)
```

这个完整的技术文档为ROS2仿真系统开发提供了全面的指导，从基础概念到高级功能实现，适合不同层次的开发者使用。
