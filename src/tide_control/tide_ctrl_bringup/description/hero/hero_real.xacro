<?xml version="1.0"?>

<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="hero">
    <xacro:include filename="$(find tide_robot_description)/urdf/base/hero_description_base.urdf.xacro" />

    <ros2_control name="TideHardwareInterface" type="system">
        <hardware>
            <plugin>tide_hw_interface/TideHardwareInterface</plugin>
            <param name="enable_virtual_control">true</param>
            <param name="need_calibration">false</param>
            <param name="can_device_count">0</param>
        </hardware>

        <joint name="pitch_joint">
            <param name="can_bus">can0</param>
            <param name="tx_id">3</param>
            <param name="offset">4649</param>
            <param name="motor_type">GM6020</param>

            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

        <joint name="yaw_joint">
            <param name="can_bus">can0</param>
            <param name="tx_id">2</param>
            <param name="offset">6668</param>
            <param name="motor_type">GM6020</param>

            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

    </ros2_control>

</robot>
