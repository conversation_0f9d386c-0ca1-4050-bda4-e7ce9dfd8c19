<?xml version="1.0"?>

<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="infantry">
    <xacro:include filename="$(find tide_robot_description)/urdf/sim/infantry_description_sim.urdf.xacro" />

    <ros2_control name="GazeboSystem" type="system">
        <hardware>
            <plugin>gazebo_ros2_control/GazeboSystem</plugin>
        </hardware>

        <joint name="pitch_joint">
            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

        <joint name="yaw_joint">
            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

    </ros2_control>

</robot>
