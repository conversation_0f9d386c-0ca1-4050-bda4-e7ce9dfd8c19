<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="loader_motor">

    <!-- Constants -->
    <xacro:property name="PI" value="3.1415926535"/>

    <!-- Materials -->
    <material name="black">
        <color rgba="0.0 0.0 0.0 1.0"/>
    </material>
    <material name="blue">
        <color rgba="0.0 0.0 0.8 1.0"/>
    </material>

    <!-- Base Link -->
    <link name="world"/>

    <!-- Motor Mount Link -->
    <link name="motor_mount">
        <visual>
            <geometry>
                <cylinder radius="0.04" length="0.08"/>
            </geometry>
            <material name="black"/>
        </visual>
        <collision>
            <geometry>
                <cylinder radius="0.04" length="0.08"/>
            </geometry>
        </collision>
        <inertial>
            <mass value="0.5"/>
            <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
        </inertial>
    </link>

    <link name="pitch_joint">
        <visual>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
            <material name="blue"/>
        </visual>
        <collision>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
        </collision>
        <inertial>
            <mass value="0.2"/>
            <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
        </inertial>
    </link>

    <link name="yaw_joint">
        <visual>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
            <material name="blue"/>
        </visual>
        <collision>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
        </collision>
        <inertial>
            <mass value="0.2"/>
            <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
        </inertial>
    </link>

    <joint name="world_to_mount" type="fixed">
        <parent link="world"/>
        <child link="motor_mount"/>
        <origin xyz="0 0 0.04" rpy="0 0 0"/>
    </joint>

    <joint name="pitch_joint" type="revolute">
        <parent link="motor_mount"/>
        <child link="pitch_joint"/>
        <origin xyz="0 0 0.06" rpy="0 0 0"/>
        <axis xyz="0 0 1"/>
        <limit lower="-${PI}" upper="${PI}" effort="1000" velocity="10"/>
    </joint>

    <joint name="yaw_joint" type="revolute">
        <parent link="motor_mount"/>
        <child link="yaw_joint"/>
        <origin xyz="0 0 0.12" rpy="0 0 0"/>
        <axis xyz="0 0 1"/>
        <limit lower="-${PI}" upper="${PI}" effort="1000" velocity="10"/>
    </joint>

    <ros2_control name="TideHardwareInterface" type="system">
        <hardware>
            <plugin>tide_hw_interface/TideHardwareInterface</plugin>
            <param name="enable_virtual_control">true</param>
            <param name="need_calibration">false</param>
            <param name="can_device_count">1</param>
        </hardware>

        <joint name="pitch_joint">
            <param name="can_bus">can0</param>
            <param name="tx_id">2</param>
            <param name="offset">1626</param>
            <param name="motor_type">GM6020</param>

            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

        <joint name="yaw_joint">
            <param name="can_bus">can0</param>
            <param name="tx_id">1</param>
            <param name="offset">6758</param>
            <param name="motor_type">GM6020</param>

            <command_interface name="position"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>

    </ros2_control>

</robot>
