<?xml version="1.0"?>
<robot xmlns:xacro="http://www.ros.org/wiki/xacro" name="loader_motor">

    <!-- Constants -->
    <xacro:property name="PI" value="3.14159"/>

    <!-- Materials -->
    <material name="black">
        <color rgba="0.0 0.0 0.0 1.0"/>
    </material>
    <material name="blue">
        <color rgba="0.0 0.0 0.8 1.0"/>
    </material>

    <!-- Base Link -->
    <link name="world"/>

    <!-- Motor Mount Link -->
    <link name="motor_mount">
        <visual>
            <geometry>
                <cylinder radius="0.04" length="0.08"/>
            </geometry>
            <material name="black"/>
        </visual>
        <collision>
            <geometry>
                <cylinder radius="0.04" length="0.08"/>
            </geometry>
        </collision>
        <inertial>
            <mass value="0.5"/>
            <inertia ixx="0.001" ixy="0.0" ixz="0.0" iyy="0.001" iyz="0.0" izz="0.001"/>
        </inertial>
    </link>

    <!-- Motor Output Link -->
    <link name="loader_motor0">
        <visual>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
            <material name="blue"/>
        </visual>
        <collision>
            <geometry>
                <cylinder radius="0.02" length="0.04"/>
            </geometry>
        </collision>
        <inertial>
            <mass value="0.2"/>
            <inertia ixx="0.0001" ixy="0.0" ixz="0.0" iyy="0.0001" iyz="0.0" izz="0.0001"/>
        </inertial>
    </link>

    <!-- Fixed Joint between World and Motor Mount -->
    <joint name="world_to_mount" type="fixed">
        <parent link="world"/>
        <child link="motor_mount"/>
        <origin xyz="0 0 0.04" rpy="0 0 0"/>
    </joint>

    <!-- Revolute Joint for Motor -->
    <joint name="loader_motor0" type="revolute">
        <parent link="motor_mount"/>
        <child link="loader_motor0"/>
        <origin xyz="0 0 0.06" rpy="0 0 0"/>
        <axis xyz="0 0 1"/>
        <limit lower="-${PI}" upper="${PI}" effort="1000" velocity="10"/>
    </joint>

    <ros2_control name="TideHardwareInterface" type="system">
        <hardware>
            <plugin>tide_hw_interface/TideHardwareInterface</plugin>
            <param name="need_calibration">false</param>
            <param name="can_device_count">1</param>
        </hardware>
        <joint name="loader_motor0">
            <param name="can_bus">can0</param>
            <param name="tx_id">5</param>
            <param name="offset">0.0</param>
            <param name="motor_type">M2006</param>

            <command_interface name="velocity"></command_interface>

            <state_interface name="position"/>
            <state_interface name="velocity"/>
            <state_interface name="effort"/>
        </joint>
    </ros2_control>

</robot>
