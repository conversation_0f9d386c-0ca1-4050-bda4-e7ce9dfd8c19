<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>tide_hw_interface</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">qinghuan</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>can_msgs</depend>
  <depend>rclcpp_components</depend>
  <depend>tide_msgs</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>lifecycle_msgs</depend>
  <depend>control_toolbox</depend>
  <depend>realtime_tools</depend>
  <depend>joint_state_publisher</depend>
  <depend>robot_state_publisher</depend>
  <depend>xacro</depend>
  <depend>rviz2</depend>

  <depend>hardware_interface</depend>
  <depend>pluginlib</depend>
  <depend>controller_manager</depend>
  <depend>controller_interface</depend>
  <depend>joint_trajectory_controller</depend>
  <depend>forward_command_controller</depend>
  <depend>ros2_control</depend>
  <depend>ros2_controllers</depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
