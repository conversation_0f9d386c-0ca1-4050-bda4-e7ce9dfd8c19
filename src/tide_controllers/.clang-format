---
BasedOnStyle:  Google
ColumnLimit:    100
MaxEmptyLinesToKeep: 1
SortIncludes: false

Standard:        Auto
IndentWidth:     2
TabWidth:        2
UseTab:          Never
AccessModifierOffset: -2
ConstructorInitializerIndentWidth: 2
NamespaceIndentation: None
ContinuationIndentWidth: 4
IndentCaseLabels: true
IndentFunctionDeclarationAfterType: false

AlignEscapedNewlinesLeft: false
AlignTrailingComments: true

AllowAllParametersOfDeclarationOnNextLine: false
ExperimentalAutoDetectBinPacking: false
ObjCSpaceBeforeProtocolList: true
Cpp11BracedListStyle: false

AllowShortBlocksOnASingleLine: true
AllowShortIfStatementsOnASingleLine: false
AllowShortLoopsOnASingleLine: false
AllowShortFunctionsOnASingleLine: true
AllowShortCaseLabelsOnASingleLine: false

AlwaysBreakTemplateDeclarations: true
AlwaysBreakBeforeMultilineStrings: false
BreakBeforeBinaryOperators: false
BreakBeforeTernaryOperators: false
BreakConstructorInitializersBeforeComma: true

BinPackParameters: true
ConstructorInitializerAllOnOneLineOrOnePerLine: true
DerivePointerBinding: false
PointerBindsToType: true

PenaltyExcessCharacter: 50
PenaltyBreakBeforeFirstCallParameter: 30
PenaltyBreakComment: 1000
PenaltyBreakFirstLessLess: 10
PenaltyBreakString: 100
PenaltyReturnTypeOnItsOwnLine: 50

SpacesBeforeTrailingComments: 2
SpacesInParentheses: false
SpacesInAngles:  false
SpaceInEmptyParentheses: false
SpacesInCStyleCastParentheses: false
SpaceAfterCStyleCast: false
SpaceAfterControlStatementKeyword: true
SpaceBeforeAssignmentOperators: true

# Configure each individual brace in BraceWrapping
BreakBeforeBraces: Custom

# Control of individual brace wrapping cases
BraceWrapping:
    AfterCaseLabel: true
    AfterClass: true
    AfterControlStatement: true
    AfterEnum: true
    AfterFunction: true
    AfterNamespace: true
    AfterStruct: true
    AfterUnion: true
    BeforeCatch: true
    BeforeElse: true
    IndentBraces: false
...
