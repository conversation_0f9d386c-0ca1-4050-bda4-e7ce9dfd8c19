<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>tide_gimbal_controller</name>
  <version>0.0.0</version>
  <description>TODO: Package description</description>
  <maintainer email="<EMAIL>">qinghuan</maintainer>
  <license>TODO: License declaration</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <build_depend>generate_parameter_library</build_depend>

  <depend>backward_ros</depend>
  <depend>controller_interface</depend>
  <depend>hardware_interface</depend>
  <depend>pluginlib</depend>
  <depend>rclcpp</depend>
  <depend>rclcpp_lifecycle</depend>
  <depend>rcpputils</depend>
  <depend>realtime_tools</depend>
  <depend>tf2</depend>
  <depend>tf2_msgs</depend>
  <depend>control_toolbox</depend>
  <depend>tide_msgs</depend>
  <depend>angles</depend>
  <depend>visualization_msgs</depend>
  <depend>tf2_geometry_msgs</depend>
  <depend>geometry_msgs</depend>

  <test_depend>ament_cmake_gmock</test_depend>
  <test_depend>controller_manager</test_depend>
  <test_depend>hardware_interface_testing</test_depend>
  <test_depend>ros2_control_test_assets</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
